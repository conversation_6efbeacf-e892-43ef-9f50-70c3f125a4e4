<!DOCTYPE html>
<html>
<head>
    <title>Submarine Cable Map</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #map {
            height: 100vh;
            width: calc(100% - 350px); /* Adjust width for left sidebar */
            margin-left: 350px; /* Make room for left sidebar */
            position: relative;
        }

        /* Sidebar Styles - Positioned on the left */
        #sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 350px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid #e0e0e0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            z-index: 1000;
            overflow-y: auto;
            padding: 20px;
            box-sizing: border-box;
        }

        #sidebar h2 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 20px;
            font-weight: 600;
        }

        /* Professional Header Styling */
        .app-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
            position: relative;
            overflow: hidden;
        }

        .app-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .app-logo {
            position: relative;
            z-index: 2;
        }

        .logo-icon {
            display: inline-block;
            width: 48px;
            height: 48px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            border-radius: 50%;
            margin-bottom: 12px;
            position: relative;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 28px;
            height: 4px;
            background: linear-gradient(90deg, #fff 0%, #e8f4fd 50%, #fff 100%);
            border-radius: 2px;
            box-shadow:
                0 -8px 0 -1px rgba(255, 255, 255, 0.8),
                0 8px 0 -1px rgba(255, 255, 255, 0.8),
                0 -16px 0 -2px rgba(255, 255, 255, 0.6),
                0 16px 0 -2px rgba(255, 255, 255, 0.6);
        }

        .logo-icon::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 6px;
            height: 6px;
            background: #fff;
            border-radius: 50%;
            box-shadow:
                -10px 0 0 -1px rgba(255, 255, 255, 0.9),
                10px 0 0 -1px rgba(255, 255, 255, 0.9);
        }

        .app-title {
            color: white;
            font-size: 18px;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
        }

        .app-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 12px;
            font-weight: 400;
            margin: 4px 0 0 0;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .search-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e8e8e8;
        }

        .search-group {
            margin-bottom: 15px;
        }

        .search-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
            font-size: 14px;
        }

        .search-input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .search-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .dropdown-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-top: none;
            border-radius: 0 0 6px 6px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }

        .dropdown-item {
            padding: 10px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .search-group {
            position: relative;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-primary:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .results-section {
            margin-top: 20px;
        }

        .results-header {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .cable-result {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease, transform 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .cable-result:hover {
            border-color: #3498db;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
            transform: translateY(-1px);
        }

        .cable-result:active {
            transform: translateY(0px) scale(0.98);
        }

        .cable-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 14px;
            display: flex;
            align-items: center;
            line-height: 1.3;
        }

        .cable-details {
            font-size: 12px;
            color: #7f8c8d;
            line-height: 1.4;
        }

        .cable-color-indicator {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
            vertical-align: middle;
            border: 2px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
            flex-shrink: 0; /* Prevent shrinking in flex layouts */
        }

        .no-results {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 20px;
        }

        .info {
            padding: 6px 8px;
            font: 14px/16px Arial, Helvetica, sans-serif;
            background: white;
            background: rgba(255,255,255,0.8);
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            border-radius: 5px;
        }
        .info h4 {
            margin: 0 0 5px;
            color: #777;
        }

        /* Responsive design for mobile devices */
        @media (max-width: 768px) {
            #sidebar {
                width: 300px; /* Slightly smaller on tablets */
            }

            #map {
                width: calc(100% - 300px);
                margin-left: 300px;
            }

            .cable-name-display {
                font-size: 12px;
                padding: 6px 10px;
                max-width: 250px;
            }

            .cable-color-indicator {
                width: 14px;
                height: 14px;
                margin-right: 8px;
            }

            .app-header {
                padding: 16px;
                margin-bottom: 20px;
            }

            .logo-icon {
                width: 40px;
                height: 40px;
                margin-bottom: 10px;
            }

            .logo-icon::before {
                font-size: 20px;
            }

            .app-title {
                font-size: 16px;
            }

            .app-subtitle {
                font-size: 11px;
            }
        }

        @media (max-width: 480px) {
            #sidebar {
                width: 100%;
                height: 35vh; /* Sidebar at top on mobile */
                position: relative;
                border-right: none;
                border-bottom: 1px solid #e0e0e0;
            }

            #map {
                width: 100%;
                margin-left: 0;
                height: 65vh; /* Map takes remaining space */
            }

            .search-section {
                margin-bottom: 15px;
                padding-bottom: 15px;
            }

            .search-group {
                margin-bottom: 10px;
            }

            .cable-name-display {
                font-size: 11px;
                padding: 5px 8px;
                max-width: 200px;
            }

            .cable-color-indicator {
                width: 12px;
                height: 12px;
                margin-right: 6px;
                border-width: 1px;
            }

            .cable-name {
                font-size: 13px;
            }

            .app-header {
                padding: 12px;
                margin-bottom: 15px;
                border-radius: 8px;
            }

            .logo-icon {
                width: 36px;
                height: 36px;
                margin-bottom: 8px;
            }

            .logo-icon::before {
                font-size: 18px;
            }

            .app-title {
                font-size: 15px;
            }

            .app-subtitle {
                font-size: 10px;
            }
        }

        /* Cable Name Label Popup Styling */
        .cable-name-popup .leaflet-popup-content-wrapper {
            background: white;
            border-radius: 6px;
            box-shadow: 0 3px 14px rgba(0,0,0,0.4);
        }

        .cable-name-popup .leaflet-popup-content {
            margin: 8px 12px;
            line-height: 1.4;
        }

        .cable-name-popup .leaflet-popup-tip {
            background: white;
        }
        
    </style>
</head>
<body>
    <div id="sidebar">
        <div class="app-header">
            <div class="app-logo">
                <div class="logo-icon"></div>
                <h1 class="app-title">Submarine CableMap</h1>
                <p class="app-subtitle">Global Undersea Cable Network Explorer</p>
            </div>
        </div>

        <div class="search-section">
            <div class="search-group">
                <label for="fromCountry">From Country:</label>
                <input type="text" id="fromCountry" class="search-input" placeholder="Select or type country name..." autocomplete="off">
                <div id="fromCountryDropdown" class="dropdown-list"></div>
            </div>

            <div class="search-group">
                <label for="toCountry">To Country:</label>
                <input type="text" id="toCountry" class="search-input" placeholder="Select or type country name..." autocomplete="off">
                <div id="toCountryDropdown" class="dropdown-list"></div>
            </div>

            <div class="search-group">
                <label for="cableType">Cable Type:</label>
                <select id="cableType" class="search-input">
                    <option value="all">All</option>
                    <option value="main">Main</option>
                    <option value="secondary">Secondary</option>
                </select>
            </div>

            <div class="button-group">
                <button id="searchBtn" class="btn btn-primary" disabled>Search Cables</button>
                <button id="clearBtn" class="btn btn-secondary">Clear Search</button>
            </div>
        </div>


        <div class="results-section">
            <div id="resultsHeader" class="results-header" style="display: none;">Found Cables:</div>
            <div id="cableResults"></div>
        </div>
        <div id="legend" style="margin-top: 30px;">
            <h4 style="margin-bottom: 8px;">Legend</h4>
            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                <span style="display:inline-block;width:24px;height:4px;background:#3498db;margin-right:8px;"></span>
                <span>Main Cable Route</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                <span style="display:inline-block;width:24px;height:4px;background:#F18F01;border-bottom:2px dashed #F18F01;margin-right:8px;"></span>
                <span>Secondary Cable Route</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                <span style="display:inline-block;width:12px;height:12px;background:#FFD700;border-radius:50%;margin-right:8px;"></span>
                <span>Highlighted Landing Point</span>
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 6px;">
                <span style="display:inline-block;width:12px;height:12px;background:#FF0000;border-radius:50%;margin-right:8px;"></span>
                <span>Landing Point</span>
            </div>
        </div>
    </div>

    <div id="map"></div>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>

        const map = L.map('map', {
            worldCopyJump: false,
            maxBounds: [[-90, -180], [90, 180]],
            maxBoundsViscosity: 1.0,
            minZoom: 2,
            maxZoom: 8
        }).setView([20, 0], 2);

        L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(map);

        const cableLayer = L.layerGroup().addTo(map);
        const landingPointLayer = L.layerGroup().addTo(map);

        // Create info control for hover information
        const info = L.control();
        info.onAdd = function (map) {
            this._div = L.DomUtil.create('div', 'info');
            this.update();
            return this._div;
        };
        info.update = function (props) {
            this._div.innerHTML = '<h4>Submarine Cable Info</h4>' +  (props ?
                '<b>' + props.name + '</b><br />' +
                (props.rfs ? 'RFS: ' + props.rfs + '<br />' : '') +
                (props.length ? 'Length: ' + props.length + ' km<br />' : '') +
                (props.owners ? 'Owners: ' + props.owners : '')
                : 'Hover over a cable');
        };
        info.addTo(map);

        const professionalColorPalette = [
            '#2E86AB','#A23B72', '#F18F01',
            '#C73E1D','#6C757D', '#495057',
            '#4A90A4', '#8E44AD', '#D35400',
            '#27AE60', '#2C3E50', '#8B4513',
            '#556B2F', '#4682B4', '#CD853F',
            '#708090', '#2F4F4F', '#800080',
            '#B22222', '#228B22', '#4169E1',
            '#DC143C', '#FF8C00', '#9932CC',
            '#8FBC8F', '#483D8B', '#2E8B57',
            '#B8860B', '#A0522D', '#1E90FF',
            '#32CD32', '#FF6347','#4B0082',
            '#DAA520', '#008B8B', '#9400D3',
            '#FF4500', '#2E8B57', '#8B008B',
            '#556B2F'
        ];

        const specialCableColors = {
            'atlantic-crossing-1-ac-1': '#FF8C00',
            '2africa': '#000000',
            'africa-coast-to-europe-ace': '#DC143C',
            'west-africa-cable-system-wacs': '#4169E1',
            'maroc-telecom-west-africa': '#9932CC',
            'sat-3wasc': '#FF6347'
        };

        function getProfessionalCableColor(cableId, index) {
            if (specialCableColors.hasOwnProperty(cableId)) {
                console.log(`🎨 Applying special color for ${cableId}: ${specialCableColors[cableId]}`);
                return specialCableColors[cableId];
            }

            let hash = 0;
            for (let i = 0; i < cableId.length; i++) {
                const char = cableId.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32-bit integer
            }

            const colorIndex = (Math.abs(hash) + index) % professionalColorPalette.length;
            return professionalColorPalette[colorIndex];
        }


        // Americas countries (North, Central, and South America)
        const americasCountries = new Set([
            // North America
            'United States', 'Canada', 'Mexico', 'Greenland',
            'Guatemala', 'Belize', 'El Salvador', 'Honduras', 'Nicaragua', 'Costa Rica', 'Panama',
            'Cuba', 'Jamaica', 'Haiti', 'Dominican Republic', 'Puerto Rico', 'Bahamas', 'Barbados',
            'Trinidad and Tobago', 'Grenada', 'Saint Vincent and the Grenadines', 'Saint Lucia',
            'Dominica', 'Antigua and Barbuda', 'Saint Kitts and Nevis', 'Martinique', 'Guadeloupe',
            'Saint Barthélemy', 'Saint Martin', 'Sint Maarten', 'Anguilla', 'British Virgin Islands',
            'Virgin Islands (U.S.)', 'Virgin Islands (U.K.)', 'Cayman Islands', 'Turks and Caicos Islands',
            'Aruba', 'Curaçao', 'Bonaire, Sint Eustatius and Saba', 'Netherlands', 'French Guiana',
            'Brazil', 'Argentina', 'Chile', 'Peru', 'Colombia', 'Venezuela', 'Ecuador', 'Bolivia',
            'Paraguay', 'Uruguay', 'Guyana', 'Suriname', 'French Guiana'
        ]);

        // Define Asia-Pacific countries and territories
        const asiaPacificCountries = new Set([
            // East Asia
            'China', 'Japan', 'South Korea', 'North Korea', 'Taiwan', 'Hong Kong', 'Macau', 'Mongolia',
            // Southeast Asia
            'Singapore', 'Indonesia', 'Philippines', 'Malaysia', 'Vietnam', 'Thailand', 'Myanmar',
            'Cambodia', 'Laos', 'Brunei', 'Timor-Leste',
            // South Asia
            'India', 'Pakistan', 'Bangladesh', 'Sri Lanka', 'Nepal', 'Bhutan', 'Maldives', 'Afghanistan',
            // Oceania and Pacific Islands
            'Australia', 'New Zealand', 'Papua New Guinea', 'Fiji', 'Solomon Islands', 'Vanuatu',
            'New Caledonia', 'Samoa', 'Tonga', 'Kiribati', 'Tuvalu', 'Nauru', 'Palau', 'Marshall Islands',
            'Micronesia', 'Cook Islands', 'French Polynesia', 'Wallis and Futuna', 'American Samoa',
            'Guam', 'Northern Mariana Islands', 'Cocos (Keeling) Islands', 'Christmas Island'
        ]);

        // Function to check if a cable has landing points in the Americas
        function isAmericasCable(cableId) {
            return false;
        }
        // Function to check if coordinates are in Americas region (rough approximation)
        function isInAmericasRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    // Americas longitude range (including Alaska and eastern Brazil)
                    // Expanded range to be more inclusive: -180° to -25°
                    return lng >= -180 && lng <= -25;
                }

                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }
                return false;
            }
            return checkCoordinates(coordinates);
        }
        // Function to check if coordinates are in Asia-Pacific region
        function isInAsiaPacificRegion(coordinates) {
            if (!coordinates || !Array.isArray(coordinates)) return false;

            // Recursive function to check coordinates at any nesting level
            function checkCoordinates(coords) {
                if (!Array.isArray(coords)) return false;

                // If this is a coordinate pair [lng, lat]
                if (coords.length === 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number') {
                    const lng = coords[0];
                    const lat = coords[1];

                    const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
                    const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

                    return inMainAsiaPacific || inPacificExtension;
                }
                // If this is an array of coordinates, check each one
                for (let item of coords) {
                    if (checkCoordinates(item)) {
                        return true;
                    }
                }
                return false;
            }

            return checkCoordinates(coordinates);
        }
        // Fetch and display cable routes
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total cables before filtering: ${data.features.length}`);

                // Critical cables that should always be preserved (African + important transatlantic)
                const criticalAfricanCables = new Set([
                    '2africa','west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'asia-africa-europe-1-aae-1',
                    'safe','sat-3wasc',
                    'equiano','africa-1',
                    'seychelles-to-east-africa-system-seas',
                    'the-east-african-marine-system-teams',
                    'seacomtata-tgn-eurasia',
                    'atlantic-crossing-1-ac-1'  // Important Europe-Americas cable (NL-UK-DE-US)
                ]);

                const filteredFeatures = data.features.filter(feature => {
                    // Always preserve critical African cables
                    if (criticalAfricanCables.has(feature.properties.id)) {
                        console.log(`Preserving critical African cable: ${feature.properties.name}`);
                        return true;
                    }

                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        const isAsiaPacific = isInAsiaPacificRegion(feature.geometry.coordinates);

                        if (isAmericas) {
                            console.log(`Filtering out Americas cable: ${feature.properties.name}`);
                            return false;
                        }
                        if (isAsiaPacific) {
                            console.log(`Filtering out Asia-Pacific cable: ${feature.properties.name}`);
                            return false;
                        }
                        return true;
                    }
                    return true;
                });

                console.log(`Total cables after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                // Create cables with professional color scheme and interactive labeling
                L.geoJSON(filteredData, {
                    style: function(feature) {
                        const cableIndex = filteredData.features.indexOf(feature);
                        return getCableStyle(feature, cableIndex);
                    },
                    onEachFeature: function(feature, layer) {
                        const cableIndex = filteredData.features.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);

                        layer.on({
                            mouseover: function(e) {
                                // Only apply hover effects if individual cable selection is not active
                                if (!isIndividualCableSelected) {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 4,
                                        opacity: 1
                                        // Removed color change - keep original color
                                    });
                                    info.update(feature.properties);
                                }
                            },
                            mouseout: function(e) {
                                // Only restore hover effects if individual cable selection is not active
                                if (!isIndividualCableSelected) {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 2.5,
                                        opacity: 0.85
                                        // Removed color change - keep original color
                                    });
                                    info.update();
                                }
                            },
                            click: function(e) {
                                console.log('🖱️ Cable clicked:', feature.properties.name, feature.properties.id);

                                // Use professional individual cable selection
                                selectIndividualCable(feature.properties.id, feature);

                                // Prevent event bubbling to map click
                                L.DomEvent.stopPropagation(e);
                            }
                        });

                        // Create enhanced popup with professional styling
                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';

                        if (feature.properties.rfs) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                        }
                        if (feature.properties.length) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                        }
                        if (feature.properties.owners) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                        }

                        popupContent += '</div>';

                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);

                // Validate cable identification after loading
                setTimeout(() => {
                    validateCableIdentification();
                    console.log('Cable map loaded with enhanced sidebar functionality');
                }, 1000);
            });

        // Function to check if landing point coordinates are in Americas region
        function isLandingPointInAmericas(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            // Americas longitude range (including Alaska and eastern Brazil)
            return lng >= -180 && lng <= -25;
        }

        // Function to check if landing point coordinates are in Asia-Pacific region
        function isLandingPointInAsiaPacific(coordinates) {
            if (!coordinates || coordinates.length < 2) return false;

            const lng = coordinates[0];
            const lat = coordinates[1];

            // Main Asia-Pacific: Longitude 65°E to 180°E, Latitude -50°S to 80°N
            // Pacific extension: Longitude -180°E to -120°W (for Pacific islands), Latitude -50°S to 80°N
            const inMainAsiaPacific = (lng >= 65 && lng <= 180) && (lat >= -50 && lat <= 80);
            const inPacificExtension = (lng >= -180 && lng <= -120) && (lat >= -50 && lat <= 80);

            return inMainAsiaPacific || inPacificExtension;
        }

        // Fetch and display landing points
        fetch('../landing-point/landing-point-geo.json')
            .then(response => response.json())
            .then(data => {
                console.log(`Total landing points before filtering: ${data.features.length}`);

                // Critical African landing points that should always be preserved
                const criticalAfricanLandingPoints = new Set([
                    'cape-town-south-africa',
                    'mtunzini-south-africa',
                    'port-elizabeth-south-africa',
                    'gqeberha-south-africa',
                    'mombasa-kenya',
                    'dar-es-salaam-tanzania',
                    'djibouti-city-djibouti','lagos-nigeria',
                    'accra-ghana','dakar-senegal',
                    'casablanca-morocco',
                    'alexandria-egypt',
                    'port-said-egypt',
                    'zafarana-egypt',
                    'mumbai-india',
                    'maputo-mozambique',
                    'jeddah-saudi-arabia'
                ]);

                // Filter out landing points in Americas and Asia-Pacific regions, but preserve critical African infrastructure
                const filteredFeatures = data.features.filter(feature => {
                    // Always preserve critical African landing points
                    if (criticalAfricanLandingPoints.has(feature.properties.id)) {
                        console.log(`Preserving critical African landing point: ${feature.properties.name}`);
                        return true;
                    }

                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isLandingPointInAmericas(feature.geometry.coordinates);
                        const isAsiaPacific = isLandingPointInAsiaPacific(feature.geometry.coordinates);

                        if (isAmericas) {
                            console.log(`Filtering out Americas landing point: ${feature.properties.name}`);
                            return false;
                        }
                        if (isAsiaPacific) {
                            console.log(`Filtering out Asia-Pacific landing point: ${feature.properties.name}`);
                            return false;
                        }
                        return true;
                    }
                    return true;
                });

                console.log(`Total landing points after filtering: ${filteredFeatures.length}`);

                // Create new GeoJSON object with filtered features
                const filteredData = {
                    ...data,
                    features: filteredFeatures
                };

                L.geoJSON(filteredData, {
                    pointToLayer: (feature, latlng) => {
                        return L.circleMarker(latlng, {
                            radius: 5,
                            fillColor: '#FF0000',
                            color: '#000',
                            weight: 1,
                            opacity: 1,
                            fillOpacity: 0.8
                        });
                    },
                    onEachFeature: (feature, layer) => {
                        if (feature.properties.name) {
                            layer.bindPopup(`<b>${feature.properties.name}</b><br>
                                ${feature.properties.country || ''}`);
                        }
                    }
                }).addTo(landingPointLayer);
            });

        // Add layer control with improved tile options
        const baseMaps = {
            "CartoDB Positron": L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "Esri World Street": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, DeLorme, NAVTEQ, USGS, Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012',
                maxZoom: 19
            }),
            "CartoDB Voyager": L.tileLayer('https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png', {
                attribution: '© OpenStreetMap contributors © CARTO',
                subdomains: 'abcd',
                maxZoom: 19
            }),
            "OpenStreetMap": L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 19
            }),
            "Satellite": L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 19
            })
        };

        const overlayMaps = {
            "Submarine Cables": cableLayer,
            "Landing Points": landingPointLayer
        };

        L.control.layers(baseMaps, overlayMaps).addTo(map);

        L.control.scale().addTo(map);

        let allCables = [];
        let allLandingPoints = [];
        let availableCountries = new Set();
        let originalCableStyles = new Map();
        let isSearchActive = false;
        let currentCableIndex = 0;
        let cableCarouselActive = false;
        let currentCableNameLabel = null; // Store current cable name label
        let selectedCableId = null; // Track currently selected cable for individual highlighting
        let isIndividualCableSelected = false; // Track if individual cable selection is active

        // ===== PROFESSIONAL VISUAL HIGHLIGHTING SYSTEM =====

        function selectIndividualCable(cableId, cableFeature) {
            console.log(`🎯 Selecting individual cable: ${cableId}`);
            console.log('Cable layer has layers:', cableLayer.getLayers().length);

            // Set individual selection state
            selectedCableId = cableId;
            isIndividualCableSelected = true;

            // Store original styles for all cables (always refresh to handle dynamic loading)
            cableLayer.eachLayer(layer => {
                if (layer.feature && !originalCableStyles.has(layer.feature.properties.id)) {
                    const cableIndex = Array.from(cableLayer.getLayers()).indexOf(layer);
                    const originalColor = getProfessionalCableColor(layer.feature.properties.id, cableIndex);
                    originalCableStyles.set(layer.feature.properties.id, {
                        color: originalColor,
                        opacity: 0.85,
                        weight: 2.5
                    });
                    console.log(`Stored style for cable: ${layer.feature.properties.id} with color: ${originalColor}`);
                }
            });

            let selectedLayer = null;

            // Apply visual highlighting: fade others, emphasize selected
            let layersProcessed = 0;
            let selectedLayerFound = false;

            // Function to process individual feature layers (handles nested layer groups)
            function processLayer(layer, depth = 0) {
                layersProcessed++;
                const indent = '  '.repeat(depth);

                if (layer.feature && layer.feature.properties) {
                    // This is an individual feature layer
                    const layerCableId = layer.feature.properties.id;
                    const layerCableName = layer.feature.properties.name;
                    console.log(`${indent}Processing feature layer ${layersProcessed}: ID="${layerCableId}", Name="${layerCableName}"`);

                    if (layerCableId === cableId) {
                        selectedLayer = layer;
                        selectedLayerFound = true;
                        console.log(`${indent}✅ Found selected cable layer: ${layerCableId}`);

                        // Get or calculate original style
                        let originalStyle = originalCableStyles.get(layerCableId);
                        if (!originalStyle) {
                            const originalColor = getProfessionalCableColor(layerCableId, layersProcessed);
                            originalStyle = {
                                color: originalColor,
                                opacity: 0.85,
                                weight: 2.5
                            };
                            originalCableStyles.set(layerCableId, originalStyle);
                        }
                        console.log(`${indent}Original style:`, originalStyle);

                        layer.setStyle({
                            color: originalStyle.color, // Explicitly restore original color
                            opacity: 1.0, // Full opacity
                            weight: 4.5, // Increased thickness for emphasis
                            dashArray: null
                        });

                        // Bring to front for better visibility
                        layer.bringToFront();
                        console.log(`${indent}🎨 Applied emphasis style to ${layerCableId}`);

                    } else {
                        // Fade all other cables - reduce opacity and lighten color
                        layer.setStyle({
                            color: '#bbb', // Light gray
                            opacity: 0.25, // Significantly faded
                            weight: 1.5, // Thinner
                            dashArray: null
                        });
                    }
                } else if (layer.eachLayer) {
                    // This is a layer group, process its children
                    console.log(`${indent}Processing layer group ${layersProcessed} with ${Object.keys(layer._layers || {}).length} children`);
                    layer.eachLayer(childLayer => processLayer(childLayer, depth + 1));
                } else {
                    console.log(`${indent}Layer ${layersProcessed}: No feature or eachLayer method`);
                }
            }

            // Process all layers (including nested ones)
            cableLayer.eachLayer(layer => processLayer(layer));

            console.log(`Processed ${layersProcessed} layers total, selected layer found: ${selectedLayerFound}`);

            // Smooth zoom to the selected cable
            if (selectedLayer) {
                try {
                    const bounds = selectedLayer.getBounds();
                    if (bounds && bounds.isValid()) {
                        map.fitBounds(bounds, {
                            padding: [60, 60], // Good padding for context
                            maxZoom: 6, // Appropriate zoom level
                            animate: true,
                            duration: 1.2 // Smooth animation
                        });

                        // Show popup after zoom completes
                        setTimeout(() => {
                            if (selectedLayer.getPopup()) {
                                selectedLayer.openPopup();
                            }
                        }, 1300);
                    }
                } catch (error) {
                    console.log('Using fallback zoom method');
                    if (cableFeature && cableFeature.geometry) {
                        zoomToCableGeometry(cableFeature);
                    }
                }
            }

            console.log(`✅ Cable ${cableId} visually highlighted`);
        }

        function clearIndividualCableSelection() {
            console.log('🔄 Clearing individual cable selection');

            if (!isIndividualCableSelected) {
                return; // Nothing to clear
            }

            selectedCableId = null;
            isIndividualCableSelected = false;

            // Function to restore styles for individual feature layers
            function restoreLayer(layer, layerIndex = 0) {
                if (layer.feature && layer.feature.properties) {
                    const cableId = layer.feature.properties.id;
                    const originalColor = getProfessionalCableColor(cableId, layerIndex);

                    // Restore to normal style
                    layer.setStyle({
                        color: originalColor,
                        opacity: 0.85,
                        weight: 2.5,
                        dashArray: layer.feature.properties.is_planned ? '8, 4' : null
                    });

                    // Close any open popups
                    if (layer.getPopup()) {
                        layer.closePopup();
                    }
                } else if (layer.eachLayer) {
                    // This is a layer group, process its children
                    layer.eachLayer(childLayer => restoreLayer(childLayer, layerIndex));
                }
            }

            // Restore all cables to their normal appearance (including nested ones)
            let layerIndex = 0;
            cableLayer.eachLayer(layer => {
                restoreLayer(layer, layerIndex);
                layerIndex++;
            });

            console.log('✅ Individual cable selection cleared, normal view restored');
        }

        function zoomToCableGeometry(cableFeature) {
            if (!cableFeature.geometry || !cableFeature.geometry.coordinates) {
                return;
            }

            const coords = cableFeature.geometry.coordinates;
            let bounds = L.latLngBounds();

            function addCoordinatesToBounds(coordinates) {
                if (Array.isArray(coordinates[0])) {
                    if (Array.isArray(coordinates[0][0])) {
                        // MultiLineString
                        coordinates.forEach(lineString => {
                            lineString.forEach(coord => {
                                bounds.extend([coord[1], coord[0]]);
                            });
                        });
                    } else {
                        // LineString
                        coordinates.forEach(coord => {
                            bounds.extend([coord[1], coord[0]]);
                        });
                    }
                }
            }

            addCoordinatesToBounds(coords);

            if (bounds.isValid()) {
                map.fitBounds(bounds, {
                    padding: [60, 60],
                    maxZoom: 6,
                    animate: true,
                    duration: 1.2
                });
            }
        }

        // Add map click handler to clear search if active and close persistent popups
        map.on('click', function(e) {
            if (isSearchActive) {
                clearSearch();
            } else if (isIndividualCableSelected) {
                // Clear individual cable selection and restore normal view
                clearIndividualCableSelection();
            } else {
                // Clear any persistent cable name labels when clicking on empty map area
                if (typeof clearCableNameLabel === 'function') {
                    clearCableNameLabel();
                }
            }
        });

        // Load cable and landing point data for sidebar
        Promise.all([
            fetch('../cable/cable-geo.json').then(response => response.json()),
            fetch('../landing-point/landing-point-geo.json').then(response => response.json())
        ]).then(([cableData, landingPointData]) => {
            allCables = cableData.features;
            allLandingPoints = landingPointData.features;

            extractAvailableCountries();
            setupSearchFunctionality();
        });

        function extractAvailableCountries() {
            const africaCountries = new Set([
                'Algeria', 'Angola', 'Benin', 'Botswana', 'Burkina Faso', 'Burundi', 'Cameroon',
                'Cape Verde', 'Central African Republic', 'Chad', 'Comoros', 'Congo', 'Congo, Dem. Rep.',
                'Congo, Rep.', 'Côte d\'Ivoire', 'Djibouti', 'Egypt', 'Equatorial Guinea', 'Eritrea',
                'Ethiopia', 'Gabon', 'Gambia', 'Ghana', 'Guinea', 'Guinea-Bissau', 'Kenya', 'Lesotho',
                'Liberia', 'Libya', 'Madagascar', 'Malawi', 'Mali', 'Mauritania', 'Mauritius', 'Morocco',
                'Mozambique', 'Namibia', 'Niger', 'Nigeria', 'Rwanda', 'São Tomé and Príncipe', 'Senegal',
                'Seychelles', 'Sierra Leone', 'Somalia', 'South Africa', 'South Sudan', 'Sudan', 'Swaziland',
                'Tanzania', 'Togo', 'Tunisia', 'Uganda', 'Zambia', 'Zimbabwe'
            ]);

            const europeCountries = new Set([
                'Albania', 'Andorra', 'Austria', 'Belarus', 'Belgium', 'Bosnia and Herzegovina', 'Bulgaria',
                'Croatia', 'Cyprus', 'Czech Republic', 'Denmark', 'Estonia', 'Finland', 'France', 'Germany',
                'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy', 'Latvia', 'Liechtenstein', 'Lithuania',
                'Luxembourg', 'Malta', 'Moldova', 'Monaco', 'Montenegro', 'Netherlands', 'North Macedonia',
                'Norway', 'Poland', 'Portugal', 'Romania', 'Russia', 'San Marino', 'Serbia', 'Slovakia',
                'Slovenia', 'Spain', 'Sweden', 'Switzerland', 'Ukraine', 'United Kingdom', 'Vatican City'
            ]);

            // Extract countries from landing points that are in Africa or Europe
            allLandingPoints.forEach(point => {
                if (point.properties && point.properties.name) {
                    const nameParts = point.properties.name.split(',');
                    if (nameParts.length >= 2) {
                        const country = nameParts[nameParts.length - 1].trim();
                        if (africaCountries.has(country) || europeCountries.has(country)) {
                            availableCountries.add(country);
                        }
                    }
                }
            });

            // Add some known countries that might be in the cable data but not easily extracted
            const knownAfricanEuropeanCountries = [
                'South Africa', 'Egypt', 'Morocco', 'Nigeria', 'Kenya', 'Ghana', 'Senegal', 'Tanzania',
                'Angola', 'Mozambique', 'Madagascar', 'Mauritius', 'Seychelles', 'Djibouti', 'Somalia',
                'United Kingdom', 'France', 'Spain', 'Italy', 'Germany', 'Netherlands', 'Portugal',
                'Greece', 'Norway', 'Denmark', 'Sweden', 'Finland', 'Ireland', 'Belgium', 'Malta',
                'Cyprus', 'Bulgaria', 'Romania', 'Croatia', 'Albania', 'Turkey', 'Russia'
            ];

            knownAfricanEuropeanCountries.forEach(country => {
                if (africaCountries.has(country) || europeCountries.has(country)) {
                    availableCountries.add(country);
                }
            });

            console.log('Available countries for search:', Array.from(availableCountries).sort());
        }

        function setupSearchFunctionality() {
            const fromCountryInput = document.getElementById('fromCountry');
            const toCountryInput = document.getElementById('toCountry');
            const fromDropdown = document.getElementById('fromCountryDropdown');
            const toDropdown = document.getElementById('toCountryDropdown');
            const searchBtn = document.getElementById('searchBtn');
            const clearBtn = document.getElementById('clearBtn');

            const countriesArray = Array.from(availableCountries).sort();

            // Setup autocomplete for both inputs
            setupAutocomplete(fromCountryInput, fromDropdown, countriesArray);
            setupAutocomplete(toCountryInput, toDropdown, countriesArray);

            // Enable search button when both countries are selected
            function updateSearchButton() {
                const fromValid = availableCountries.has(fromCountryInput.value);
                const toValid = availableCountries.has(toCountryInput.value);
                searchBtn.disabled = !(fromValid && toValid && fromCountryInput.value !== toCountryInput.value);

                // Auto-clear search when both fields are empty
                if (fromCountryInput.value === '' && toCountryInput.value === '' && isSearchActive) {
                    console.log('🔄 Auto-clearing search due to empty input fields');

                    // Brief visual feedback that search is being cleared
                    const resultsDiv = document.getElementById('cableResults');
                    if (resultsDiv) {
                        resultsDiv.innerHTML = '<div class="no-results">🔄 Restoring original map view...</div>';
                        setTimeout(() => {
                            clearSearchResults();
                        }, 300); // Small delay for visual feedback
                    } else {
                        clearSearchResults();
                    }
                }
            }

            fromCountryInput.addEventListener('input', updateSearchButton);
            toCountryInput.addEventListener('input', updateSearchButton);

            // Additional event listeners to catch all ways of clearing input
            fromCountryInput.addEventListener('keyup', updateSearchButton);
            toCountryInput.addEventListener('keyup', updateSearchButton);
            fromCountryInput.addEventListener('change', updateSearchButton);
            toCountryInput.addEventListener('change', updateSearchButton);

            // Search functionality
            searchBtn.addEventListener('click', performSearch);
            clearBtn.addEventListener('click', clearSearch);
        }

        function setupAutocomplete(input, dropdown, countries) {
            input.addEventListener('input', function() {
                const value = this.value.toLowerCase();
                dropdown.innerHTML = '';

                if (value.length === 0) {
                    dropdown.style.display = 'none';
                    return;
                }

                const filtered = countries.filter(country =>
                    country.toLowerCase().includes(value)
                ).slice(0, 10); // Limit to 10 results

                if (filtered.length > 0) {
                    filtered.forEach(country => {
                        const item = document.createElement('div');
                        item.className = 'dropdown-item';
                        item.textContent = country;
                        item.addEventListener('click', function() {
                            input.value = country;
                            dropdown.style.display = 'none';
                            input.dispatchEvent(new Event('input'));
                        });
                        dropdown.appendChild(item);
                    });
                    dropdown.style.display = 'block';
                } else {
                    dropdown.style.display = 'none';
                }
            });

            // Hide dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!input.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });
        }

        function getCableStyle(feature, cableIndex) {
            const cableColor = getProfessionalCableColor(feature.properties.id, cableIndex);
            const priority = feature.properties.priority || 'main';
            if (priority === 'main') {
                return {
                    color: cableColor,
                    weight: 3.5,
                    opacity: 0.85,
                    dashArray: null
                };
            } else {
                // Secondary/alternative: orange dashed
                return {
                    color: '#F18F01',
                    weight: 2.5,
                    opacity: 0.85,
                    dashArray: '6, 6'
                };
            }
        }

        // --- ENHANCED: Show only matching cables on search ---
        let allCableGeoJSON = null;
        let currentCableGeoJSON = null;

        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                allCableGeoJSON = data;
            });

        // Helper to clear and add only matching cables
        function showOnlyCables(connectingCables) {
            cableLayer.clearLayers();
            if (!connectingCables || connectingCables.length === 0) return;
            // Build a GeoJSON with only the matching features
            const features = connectingCables.map(cable => cable);
            const filteredGeoJSON = {
                ...allCableGeoJSON,
                features: features.map(c => c.feature || c)
            };
            currentCableGeoJSON = filteredGeoJSON;
            L.geoJSON(filteredGeoJSON, {
                style: function(feature) {
                    const cableIndex = filteredGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    const cableIndex = filteredGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                    layer.on({
                        mouseover: function(e) {
                            // Only apply hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1
                                    // Removed color change - keep original color
                                });
                                info.update(feature.properties);
                            }
                        },
                        mouseout: function(e) {
                            // Only restore hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85
                                    // Removed color change - keep original color
                                });
                                info.update();
                            }
                        },
                        click: function(e) {
                            console.log('🖱️ Cable clicked:', feature.properties.name, feature.properties.id);
                            selectIndividualCable(feature.properties.id, feature);
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }

        // Helper to restore all cables
        function restoreAllCables() {
            cableLayer.clearLayers();
            if (!allCableGeoJSON) return;
            L.geoJSON(allCableGeoJSON, {
                style: function(feature) {
                    const cableIndex = allCableGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    // (reuse your onEachFeature code from above)
                    const cableIndex = allCableGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                    layer.on({
                        mouseover: function(e) {
                            // Only apply hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1
                                    // Removed color change - keep original color
                                });
                                info.update(feature.properties);
                            }
                        },
                        mouseout: function(e) {
                            // Only restore hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85
                                    // Removed color change - keep original color
                                });
                                info.update();
                            }
                        },
                        click: function(e) {
                            console.log('🖱️ Cable clicked:', feature.properties.name, feature.properties.id);
                            selectIndividualCable(feature.properties.id, feature);
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }
        // --- END ENHANCED ---

        async function performSearch() {
            const fromCountry = document.getElementById('fromCountry').value;
            const toCountry = document.getElementById('toCountry').value;
            const cableType = document.getElementById('cableType').value;

            console.log(`🔍 Searching for cables between ${fromCountry} and ${toCountry} (type: ${cableType})`);

            // Show loading state
            const searchBtn = document.getElementById('searchBtn');
            const originalText = searchBtn.textContent;
            searchBtn.textContent = 'Searching...';
            searchBtn.disabled = true;

            try {
                // Find cables connecting these countries
                let connectingCables = await findConnectingCables(fromCountry, toCountry);
                if (cableType !== 'all') {
                    connectingCables = connectingCables.filter(cable => {
                        const priority = (cable.cableData && cable.cableData.priority) || (cable.properties && cable.properties.priority) || 'main';
                        return priority === cableType;
                    });
                }

                console.log(`✅ Found ${connectingCables.length} connecting cables`);

                showOnlyCables(connectingCables);

                highlightLandingPointsForCables(connectingCables);

                // Display results in sidebar
                displaySearchResults(connectingCables, fromCountry, toCountry);

                isSearchActive = true;

                // Show success message
                if (connectingCables.length > 0) {
                    console.log(`🎯 Highlighted ${connectingCables.length} cables, made others barely visible (opacity 0.1)`);
                } else {
                    console.log(`❌ No cables found between ${fromCountry} and ${toCountry}`);
                }

            } catch (error) {
                console.error('Search failed:', error);
                document.getElementById('cableResults').innerHTML = '<div class="no-results">Search failed. Please try again.</div>';
            } finally {
                // Restore search button
                searchBtn.textContent = originalText;
                searchBtn.disabled = false;
            }
        }

        function clearSearchResults() {
            console.log('🔄 Clearing search results and restoring original view');

            // Hide dropdowns
            document.getElementById('fromCountryDropdown').style.display = 'none';
            document.getElementById('toCountryDropdown').style.display = 'none';

            // Hide results
            document.getElementById('resultsHeader').style.display = 'none';
            document.getElementById('cableResults').innerHTML = '';

            // Clear cable name label
            clearCableNameLabel();

            restoreAfricaEuropeCables();
            resetLandingPointHighlights();

            isSearchActive = false;

            console.log('✅ Search results cleared, map restored to original filtered state');
        }

        function clearSearch() {
            console.log('🧹 Clearing search and restoring original view');

            // Reset input fields
            document.getElementById('fromCountry').value = '';
            document.getElementById('toCountry').value = '';
            document.getElementById('searchBtn').disabled = true;

            // Clear search results (this will handle the visual reset)
            clearSearchResults();

            console.log('✅ Search cleared completely, all inputs and results reset');
        }

        async function findConnectingCables(fromCountry, toCountry) {
            const connectingCables = [];

            // Show loading indicator
            document.getElementById('cableResults').innerHTML = '<div class="no-results">Searching cables...</div>';

            // We need to load individual cable files to get landing point information
            const promises = allCables.map(async (cable) => {
                try {
                    const cableResponse = await fetch(`../cable/${cable.properties.id}.json`);
                    if (!cableResponse.ok) {
                        throw new Error(`HTTP ${cableResponse.status}`);
                    }
                    const cableData = await cableResponse.json();

                    if (cableData.landing_points && Array.isArray(cableData.landing_points)) {
                        const countries = cableData.landing_points.map(lp => lp.country).filter(Boolean);

                        if (countries.includes(fromCountry) && countries.includes(toCountry)) {
                            return {
                                ...cable,
                                cableData: cableData
                            };
                        }
                    }
                } catch (error) {
                    console.warn(`Could not load cable data for ${cable.properties.id}:`, error);
                }
                return null;
            });

            // Wait for all promises to resolve and filter out null results
            const results = await Promise.all(promises);
            const validResults = results.filter(result => result !== null);

            console.log(`Found ${validResults.length} cables connecting ${fromCountry} and ${toCountry}`);
            return validResults;
        }

        function updateMapVisualization(connectingCables) {
            console.log(`Highlighting ${connectingCables.length} cables and making others barely visible`);

            // Store original styles if not already stored
            if (originalCableStyles.size === 0) {
                cableLayer.eachLayer(layer => {
                    if (layer.feature) {
                        originalCableStyles.set(layer.feature.properties.id, {
                            color: layer.options.color,
                            opacity: layer.options.opacity,
                            weight: layer.options.weight
                        });
                    }
                });
            }

            const connectingCableIds = new Set(connectingCables.map(cable => cable.properties.id));

            // Update all cable layers with extreme visibility contrast (like submarinecablemap.com)
            cableLayer.eachLayer(layer => {
                if (layer.feature) {
                    const cableId = layer.feature.properties.id;

                    if (connectingCableIds.has(cableId)) {
                        const originalStyle = originalCableStyles.get(cableId);
                        layer.setStyle({
                            color: originalStyle.color, // Keep original bright color
                            opacity: 1.0, // Full opacity for maximum visibility
                            weight: 4, // Thicker for better visibility
                            dashArray: null // Remove any dashing for solid lines
                        });

                        // Bring highlighted cables to front
                        layer.bringToFront();

                        console.log(`Highlighted cable: ${layer.feature.properties.name} with color ${originalStyle.color}`);
                    } else {
                        // BARELY VISIBLE: Make all other cables almost invisible (like submarinecablemap.com)
                        layer.setStyle({
                            color: '#cccccc', // Very light gray color
                            opacity: 0.1, // Extremely low opacity - barely visible
                            weight: 1, // Very thin lines
                            dashArray: null
                        });
                    }
                }
            });

            // Count total cables for verification
            let totalCables = 0;
            let highlightedCount = 0;
            let dimmedCount = 0;

            cableLayer.eachLayer(layer => {
                if (layer.feature) {
                    totalCables++;
                    const cableId = layer.feature.properties.id;
                    if (connectingCableIds.has(cableId)) {
                        highlightedCount++;
                    } else {
                        dimmedCount++;
                    }
                }
            });

            console.log(`Map visualization updated:`);
            console.log(`- Total cables: ${totalCables}`);
            console.log(`- Highlighted (opacity 1.0): ${highlightedCount}`);
            console.log(`- Barely visible (opacity 0.1): ${dimmedCount}`);
            console.log(`- Contrast ratio: Highlighted cables are 10x more visible than others`);
        }

        function resetMapVisualization() {
            console.log('Resetting map visualization to original state');

            // Restore original styles with smooth transitions
            cableLayer.eachLayer(layer => {
                if (layer.feature && originalCableStyles.has(layer.feature.properties.id)) {
                    const originalStyle = originalCableStyles.get(layer.feature.properties.id);

                    // Apply original style smoothly
                    layer.setStyle({
                        color: originalStyle.color,
                        opacity: originalStyle.opacity,
                        weight: originalStyle.weight,
                        dashArray: layer.feature.properties.is_planned ? '8, 4' : null
                    });
                    if (layer.getPopup()) {
                        layer.closePopup();
                    }
                }
            });
        }

        function displaySearchResults(connectingCables, fromCountry, toCountry) {
            const resultsHeader = document.getElementById('resultsHeader');
            const cableResults = document.getElementById('cableResults');

            if (connectingCables.length === 0) {
                resultsHeader.style.display = 'block';
                resultsHeader.textContent = 'No cables found';
                cableResults.innerHTML = '<div class="no-results">No submarine cables found connecting these countries.</div>';
                return;
            }

            resultsHeader.style.display = 'block';
            resultsHeader.textContent = `Found ${connectingCables.length} cable${connectingCables.length > 1 ? 's' : ''}:`;

            cableResults.innerHTML = '';

            connectingCables.forEach(cable => {
                const cableDiv = document.createElement('div');
                cableDiv.className = 'cable-result';

                const actualCableColor = getActualCableColor(cable.properties.id);
                const cableData = cable.cableData;

                cableDiv.innerHTML = `
                    <div class="cable-name">
                        <span class="cable-color-indicator" style="background-color: ${actualCableColor}"></span>
                        ${cable.properties.name}
                    </div>
                    <div class="cable-details">
                        ${cableData.length ? `Length: ${cableData.length}` : ''}
                        ${cableData.rfs ? ` • RFS: ${cableData.rfs}` : ''}
                        ${cableData.owners ? `<br>Owners: ${cableData.owners}` : ''}
                    </div>
                `;

                // Add enhanced click handler with visual feedback
                cableDiv.addEventListener('click', () => {
                    // Add visual feedback to the clicked result
                    addClickFeedback(cableDiv);

                    // Center map and highlight cable using professional selection
                    centerMapOnCable(cable);
                    selectIndividualCable(cable.properties.id, cable);
                });

                cableResults.appendChild(cableDiv);
            });
        }

        function getActualCableColor(cableId) {
            let actualColor = '#3498db';

            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties.id === cableId) {
                    if (originalCableStyles.has(cableId)) {
                        actualColor = originalCableStyles.get(cableId).color;
                    } else if (layer.options.color) {
                        actualColor = layer.options.color;
                    } else if (layer.feature.properties.color) {
                        actualColor = layer.feature.properties.color;
                    }

                    if (!actualColor || actualColor === 'undefined') {
                        actualColor = '#3498db';
                    }
                }
            });

            return actualColor;
        }

        function validateCableIdentification() {
            let identifiedCables = 0;
            let totalCables = 0;

            cableLayer.eachLayer(layer => {
                totalCables++;
                if (layer.feature && layer.feature.properties.id) {
                    identifiedCables++;
                } else {
                    console.warn('Cable layer without proper identification:', layer);
                }
            });

            console.log(`Cable identification: ${identifiedCables}/${totalCables} cables properly identified`);
            return identifiedCables === totalCables;
        }

        function addClickFeedback(element) {
            // Add a brief visual feedback when cable result is clicked
            element.style.transform = 'scale(0.98)';
            element.style.backgroundColor = '#e8f4fd';
            element.style.borderColor = '#3498db';

            setTimeout(() => {
                element.style.transform = 'scale(1)';
                element.style.backgroundColor = 'white';
                element.style.borderColor = '#e8e8e8';
            }, 200);
        }

        function centerMapOnCable(cable) {
            if (cable.geometry && cable.geometry.coordinates) {
                // Calculate bounds of the cable
                const coords = cable.geometry.coordinates;
                let bounds = L.latLngBounds();

                function addCoordinatesToBounds(coordinates) {
                    if (Array.isArray(coordinates[0])) {
                        if (Array.isArray(coordinates[0][0])) {
                            // MultiLineString
                            coordinates.forEach(lineString => {
                                lineString.forEach(coord => {
                                    bounds.extend([coord[1], coord[0]]);
                                });
                            });
                        } else {
                            // LineString
                            coordinates.forEach(coord => {
                                bounds.extend([coord[1], coord[0]]);
                            });
                        }
                    }
                }
                 addCoordinatesToBounds(coords);

                if (bounds.isValid()) {
                    // Enhanced map centering with better padding and zoom control
                    map.fitBounds(bounds, {
                        padding: [30, 30],
                        maxZoom: 6 // Prevent zooming in too much for very short cables
                    });
                }
            }
        }

        // ===== SIMPLE CABLE NAME LABEL FUNCTIONS =====

        function showCableNameLabel(cable) {
            // Clear any existing label
            clearCableNameLabel();

            if (!cable || !cable.properties || !cable.properties.name) {
                console.log('No cable name available for label');
                return;
            }

            // Find the cable layer to get its geometry
            let cableLayer = null;

            // Search through all cable layers to find the matching one
            map.eachLayer(layer => {
                if (layer.feature && layer.feature.properties &&
                    layer.feature.properties.id === cable.properties.id) {
                    cableLayer = layer;
                }
            });

            if (!cableLayer) {
                console.log('Cable layer not found for label');
                return;
            }

            // Get a point on the cable line for label placement
            let labelPosition = null;

            if (cableLayer.feature.geometry.type === 'LineString') {
                // Get coordinates from the cable geometry
                const coordinates = cableLayer.feature.geometry.coordinates;
                if (coordinates && coordinates.length > 0) {
                    // Use the middle point of the cable line
                    const middleIndex = Math.floor(coordinates.length / 2);
                    const coord = coordinates[middleIndex];
                    labelPosition = L.latLng(coord[1], coord[0]); // Note: GeoJSON is [lng, lat]
                }
            } else if (cableLayer.feature.geometry.type === 'MultiLineString') {
                // For MultiLineString, use the first line's middle point
                const firstLine = cableLayer.feature.geometry.coordinates[0];
                if (firstLine && firstLine.length > 0) {
                    const middleIndex = Math.floor(firstLine.length / 2);
                    const coord = firstLine[middleIndex];
                    labelPosition = L.latLng(coord[1], coord[0]);
                }
            }

            // Fallback to bounds center if geometry method fails
            if (!labelPosition) {
                const bounds = cableLayer.getBounds();
                if (bounds && bounds.isValid()) {
                    labelPosition = bounds.getCenter();
                } else {
                    console.log('Could not determine label position for cable');
                    return;
                }
            }

            // Create popup content with same styling as existing cable popups (just the name)
            const popupContent = `
                <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                    <h4 style="margin: 0; color: #2c3e50; font-size: 14px;">${cable.properties.name}</h4>
                </div>
            `;

            // Create and show the popup directly on the cable line
            currentCableNameLabel = L.popup({
                closeButton: true,
                autoClose: false,
                closeOnClick: false,
                className: 'cable-name-popup'
            })
            .setLatLng(labelPosition)
            .setContent(popupContent)
            .openOn(map);

            // Note: Removed auto-close timeout to keep tooltip persistent until user action
        }

        function clearCableNameLabel() {
            if (currentCableNameLabel) {
                map.closePopup(currentCableNameLabel);
                currentCableNameLabel = null;
            }
        }

        function highlightSpecificCable(cableId) {
            console.log(`Highlighting cable: ${cableId}`);

            // Find the cable data for the name label
            let selectedCable = null;
            allCables.forEach(cable => {
                if (cable.properties && cable.properties.id === cableId) {
                    selectedCable = cable;
                }
            });

            // Show cable name label if cable data is available
            if (selectedCable) {
                showCableNameLabel(selectedCable);
            }

            // Find and highlight the specific cable with enhanced visual effects
            cableLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties.id === cableId) {
                    const originalStyle = originalCableStyles.get(cableId);

                    // Create a pulsing highlight effect
                    const pulseHighlight = () => {
                        // First pulse - maximum highlight
                        layer.setStyle({
                            color: originalStyle.color,
                            opacity: 1.0,
                            weight: 8, // Thick line for maximum visibility
                            dashArray: null // Remove any dashing
                        });

                        // Add a subtle glow effect by creating a temporary shadow layer
                        setTimeout(() => {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 0.9,
                                weight: 7
                            });
                        }, 300);

                        setTimeout(() => {
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 1.0,
                                weight: 8
                            });
                        }, 600);
                    };

                    // Start the pulse effect
                    pulseHighlight();

                    // Show popup with cable info if available
                    if (layer.getPopup()) {
                        // Find a good position for the popup (center of cable bounds)
                        const bounds = layer.getBounds();
                        if (bounds && bounds.isValid()) {
                            const center = bounds.getCenter();
                            layer.openPopup(center);
                        } else {
                            layer.openPopup();
                        }
                    }

                    // Reset to search result state after 4 seconds (but keep popup open)
                    setTimeout(() => {
                        if (isSearchActive) {
                            // Return to search result highlighting (not original state)
                            layer.setStyle({
                                color: originalStyle.color,
                                opacity: 1.0,
                                weight: 3.5 // Back to search result weight
                            });
                        } else {
                            // If search was cleared, return to original state
                            layer.setStyle(originalStyle);
                        }

                        // Note: Removed popup auto-close to keep tooltip persistent until user action
                    }, 4000);

                    // Bring this layer to front for better visibility
                    layer.bringToFront();
                }
            });
        }

        // --- ENHANCED: Highlight landing points for searched cables ---
        let highlightedLandingPointIds = new Set();
        function highlightLandingPointsForCables(connectingCables) {
            highlightedLandingPointIds.clear();
            connectingCables.forEach(cable => {
                if (cable.cableData && cable.cableData.landing_points) {
                    cable.cableData.landing_points.forEach(lp => {
                        if (lp.id) highlightedLandingPointIds.add(lp.id);
                    });
                }
            });
            landingPointLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties && layer.feature.properties.id) {
                    if (highlightedLandingPointIds.has(layer.feature.properties.id)) {
                        layer.setStyle({ fillColor: '#FFD700', radius: 10 }); // Gold and larger
                        layer.bringToFront();
                    } else {
                        layer.setStyle({ fillColor: '#FF0000', radius: 5 }); // Default
                    }
                }
            });
        }
        function resetLandingPointHighlights() {
            highlightedLandingPointIds.clear();
            landingPointLayer.eachLayer(layer => {
                if (layer.feature && layer.feature.properties && layer.feature.properties.id) {
                    layer.setStyle({ fillColor: '#FF0000', radius: 5 });
                }
            });
        }
        // --- END ENHANCED ---

        // Store the filtered Africa/Europe cables GeoJSON
        let filteredAfricaEuropeGeoJSON = null;

        // When you first filter and display Africa/Europe cables, save that filtered GeoJSON
        fetch('../cable/cable-geo.json')
            .then(response => response.json())
            .then(data => {
                // Critical cables that should always be preserved (African + important transatlantic)
                const criticalAfricanCables = new Set([
                    '2africa',
                    'west-africa-cable-system-wacs',
                    'africa-coast-to-europe-ace',
                    'eastern-africa-submarine-system-eassy',
                    'asia-africa-europe-1-aae-1',
                    'safe',
                    'sat-3wasc',
                    'equiano',
                    'africa-1',
                    'seychelles-to-east-africa-system-seas',
                    'the-east-african-marine-system-teams',
                    'seacomtata-tgn-eurasia',
                    'atlantic-crossing-1-ac-1'  // Important Europe-Americas cable (NL-UK-DE-US)
                ]);
                // Filter out Americas and Asia-Pacific cables, keep only Africa/Europe and critical cables
                const filteredFeatures = data.features.filter(feature => {
                    if (criticalAfricanCables.has(feature.properties.id)) return true;
                    if (feature.geometry && feature.geometry.coordinates) {
                        const isAmericas = isInAmericasRegion(feature.geometry.coordinates);
                        const isAsiaPacific = isInAsiaPacificRegion(feature.geometry.coordinates);
                        if (isAmericas || isAsiaPacific) return false;
                        return true;
                    }
                    return true;
                });
                filteredAfricaEuropeGeoJSON = {
                    ...data,
                    features: filteredFeatures
                };
                // Create cables with professional color scheme and interactive labeling
                L.geoJSON(filteredAfricaEuropeGeoJSON, {
                    style: function(feature) {
                        const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                        return getCableStyle(feature, cableIndex);
                    },
                    onEachFeature: function(feature, layer) {
                        // Store original style for hover effects
                        const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                        const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);

                        layer.on({
                            mouseover: function(e) {
                                // Only apply hover effects if individual cable selection is not active
                                if (!isIndividualCableSelected) {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 4,
                                        opacity: 1
                                        // Removed color change - keep original color
                                    });
                                    info.update(feature.properties);
                                }
                            },
                            mouseout: function(e) {
                                // Only restore hover effects if individual cable selection is not active
                                if (!isIndividualCableSelected) {
                                    const layer = e.target;
                                    layer.setStyle({
                                        weight: 2.5,
                                        opacity: 0.85
                                        // Removed color change - keep original color
                                    });
                                    info.update();
                                }
                            },
                            click: function(e) {
                                console.log('🖱️ Cable clicked:', feature.properties.name, feature.properties.id);
                                selectIndividualCable(feature.properties.id, feature);
                                L.DomEvent.stopPropagation(e);
                            }
                        });

                        let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                        popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';

                        if (feature.properties.rfs) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                        }
                        if (feature.properties.length) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                        }
                        if (feature.properties.owners) {
                            popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                        }

                        popupContent += '</div>';

                        layer.bindPopup(popupContent);
                    }
                }).addTo(cableLayer);
            });

        // Function to restore the Africa/Europe filtered cables
        function restoreAfricaEuropeCables() {
            cableLayer.clearLayers();
            if (!filteredAfricaEuropeGeoJSON) return;
            L.geoJSON(filteredAfricaEuropeGeoJSON, {
                style: function(feature) {
                    const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                    return getCableStyle(feature, cableIndex);
                },
                onEachFeature: function(feature, layer) {
                    const cableIndex = filteredAfricaEuropeGeoJSON.features.indexOf(feature);
                    const originalColor = getProfessionalCableColor(feature.properties.id, cableIndex);
                    layer.on({
                        mouseover: function(e) {
                            // Only apply hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 4,
                                    opacity: 1
                                    // Removed color change - keep original color
                                });
                                info.update(feature.properties);
                            }
                        },
                        mouseout: function(e) {
                            // Only restore hover effects if individual cable selection is not active
                            if (!isIndividualCableSelected) {
                                const layer = e.target;
                                layer.setStyle({
                                    weight: 2.5,
                                    opacity: 0.85
                                    // Removed color change - keep original color
                                });
                                info.update();
                            }
                        },
                        click: function(e) {
                            console.log('🖱️ Cable clicked:', feature.properties.name, feature.properties.id);
                            selectIndividualCable(feature.properties.id, feature);
                            L.DomEvent.stopPropagation(e);
                        }
                    });
                    let popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;">';
                    popupContent += '<h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px;">' + feature.properties.name + '</h4>';
                    if (feature.properties.rfs) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Ready for Service:</strong> ' + feature.properties.rfs + '</div>';
                    }
                    if (feature.properties.length) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Length:</strong> ' + feature.properties.length + ' km</div>';
                    }
                    if (feature.properties.owners) {
                        popupContent += '<div style="margin: 4px 0; font-size: 12px;"><strong>Owners:</strong> ' + feature.properties.owners + '</div>';
                    }
                    popupContent += '</div>';
                    layer.bindPopup(popupContent);
                }
            }).addTo(cableLayer);
        }
    </script>
</body>
</html>
